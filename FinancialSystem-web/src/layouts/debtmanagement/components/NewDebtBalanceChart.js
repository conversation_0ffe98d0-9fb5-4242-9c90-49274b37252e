import React from 'react';
import PropTypes from 'prop-types';
import ChartJS from '../../../utils/chartConfig';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Bar } from 'react-chartjs-2';
import { Button, Box, Alert, AlertTitle } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import WarningIcon from '@mui/icons-material/Warning';

// 注册 DataLabels 插件
ChartJS.register(ChartDataLabels);

/**
 * 各单位新增债权余额图表组件
 * @param {Object} props
 * @param {Array} props.data - 数据数组，每项包含 { companyName, newDebtAmount, disposalAmount, remainingBalance }
 * @param {String} props.title - 图表标题
 * @returns {JSX.Element}
 */
const NewDebtBalanceChart = ({ data, title = '各单位新增债权余额' }) => {
  // const [showDetailModal, setShowDetailModal] = useState(false); // 未来功能预留

  // 数据验证和处理
  const validData = (data || []).filter(
    item =>
      item &&
      item.companyName &&
      typeof item.companyName === 'string' &&
      (item.newDebtAmount > 0 || item.disposalAmount > 0 || item.remainingBalance > 0),
  );

  // 如果没有有效数据，显示暂无数据提示
  if (!validData || validData.length === 0) {
    return (
      <Box
        className="p-6 bg-white rounded-lg shadow-md"
        sx={{
          position: 'relative',
          width: '100%',
          maxWidth: '100%',
          marginBottom: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '400px',
        }}
      >
        <Box sx={{ textAlign: 'center', color: 'text.secondary' }}>
          <Box sx={{ fontSize: '16px', mb: 1 }}>{title}</Box>
          <Box sx={{ fontSize: '14px' }}>暂无数据</Box>
        </Box>
      </Box>
    );
  }

  // 按新增债权金额降序排序
  const sortedData = [...validData].sort((a, b) => (b.newDebtAmount || 0) - (a.newDebtAmount || 0));

  // 提取标签和数据
  const labels = sortedData.map(item => item.companyName);
  const newDebtAmounts = sortedData.map(item => item.newDebtAmount || 0);
  const disposalAmounts = sortedData.map(item => item.disposalAmount || 0);
  const remainingBalances = sortedData.map(item => item.remainingBalance || 0);

  // 数据一致性验证
  const dataInconsistencies = [];
  sortedData.forEach((item, index) => {
    const calculated = newDebtAmounts[index] - disposalAmounts[index];
    const actual = remainingBalances[index];
    const difference = Math.abs(actual - calculated);
    if (difference > 0.01) {
      // 差异超过0.01万元认为不一致
      dataInconsistencies.push({
        company: item.companyName,
        difference: difference,
        calculated: calculated,
        actual: actual,
      });
    }
  });

  const hasDataInconsistency = dataInconsistencies.length > 0;

  // 图表数据配置
  const chartData = {
    labels,
    datasets: [
      {
        label: '新增债权金额',
        data: newDebtAmounts,
        backgroundColor: 'rgba(39, 174, 96, 0.8)', // 绿色系 - 新增债权
        borderColor: 'rgba(39, 174, 96, 1)',
        borderWidth: 1,
        barPercentage: 0.7,
        categoryPercentage: 0.8,
      },
      {
        label: '已处置金额',
        data: disposalAmounts,
        backgroundColor: 'rgba(46, 134, 193, 0.8)', // 蓝色系 - 处置金额
        borderColor: 'rgba(46, 134, 193, 1)',
        borderWidth: 1,
        barPercentage: 0.7,
        categoryPercentage: 0.8,
      },
      {
        label: '债权余额',
        data: remainingBalances,
        backgroundColor: 'rgba(231, 76, 60, 0.8)', // 红色系 - 债权余额
        borderColor: 'rgba(231, 76, 60, 1)',
        borderWidth: 1,
        barPercentage: 0.7,
        categoryPercentage: 0.8,
      },
    ],
  };

  // 图表选项配置
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
        labels: {
          boxWidth: 20,
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12,
          },
        },
      },
      title: {
        display: true,
        text: title,
        font: {
          size: 16,
          weight: 'bold',
        },
        padding: {
          top: 10,
          bottom: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleFont: {
          size: 14,
          weight: 'medium',
        },
        bodyFont: {
          size: 12,
        },
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          label: function (context) {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            return `${label}: ${value.toFixed(2)} 万元`;
          },
          afterBody: function (context) {
            if (context.length > 0) {
              const dataIndex = context[0].dataIndex;
              // const item = sortedData[dataIndex]; // 未来功能预留
              const processed = disposalAmounts[dataIndex];
              // const remaining = remainingBalances[dataIndex]; // 未来功能预留
              const processRate =
                newDebtAmounts[dataIndex] > 0
                  ? ((processed / newDebtAmounts[dataIndex]) * 100).toFixed(1)
                  : '0.0';

              return [
                `处置比例: ${processRate}%`,
                `剩余比例: ${(100 - parseFloat(processRate)).toFixed(1)}%`,
              ];
            }
            return [];
          },
        },
      },
      datalabels: {
        anchor: 'end',
        align: 'start',
        offset: -2,
        color: '#000',
        font: {
          size: 10,
          weight: 'bold',
        },
        formatter(value) {
          return value > 0 ? Math.round(value).toLocaleString() : '';
        },
        display: function (context) {
          // 只显示大于0的数值
          return context.parsed.y > 0;
        },
      },
    },
    scales: {
      y: {
        type: 'linear',
        title: {
          display: true,
          text: '金额 (万元)',
          font: {
            size: 12,
          },
        },
        beginAtZero: true,
        ticks: {
          callback: function (value) {
            return value.toFixed(0);
          },
        },
      },
      x: {
        grid: {
          display: false, // 移除X轴网格线
        },
        ticks: {
          font: {
            size: 11,
          },
          maxRotation: labels.length > 6 ? 45 : 0,
          minRotation: 0,
          callback: function (value, index) {
            const label = labels[index];
            // 如果公司名称过长，截断显示
            return label && label.length > 10 ? label.substring(0, 10) + '...' : label;
          },
        },
      },
    },
    layout: {
      padding: {
        left: 10,
        right: 10,
        top: 20,
        bottom: 10,
      },
    },
  };

  // 处理更多信息按钮点击事件
  const handleMoreInfo = () => {
    // setShowDetailModal(true); // 未来功能预留
    console.log('详细信息功能开发中...');
  };

  // 计算汇总数据
  const totalNewDebtAmount = newDebtAmounts.reduce((sum, amount) => sum + amount, 0);
  const totalDisposalAmount = disposalAmounts.reduce((sum, amount) => sum + amount, 0);
  const totalRemainingBalance = remainingBalances.reduce((sum, amount) => sum + amount, 0);
  const totalProcessRate =
    totalNewDebtAmount > 0 ? ((totalDisposalAmount / totalNewDebtAmount) * 100).toFixed(1) : '0.0';

  return (
    <Box
      className="p-6 bg-white rounded-lg shadow-md"
      sx={{
        position: 'relative',
        width: '100%',
        maxWidth: '100%',
        marginBottom: '20px',
        overflow: 'hidden', // 防止内容溢出
      }}
    >
      {/* 更多信息按钮 */}
      <Button
        variant="text"
        size="small"
        startIcon={<InfoOutlinedIcon />}
        onClick={handleMoreInfo}
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          zIndex: 1,
          color: 'primary.main',
          '&:hover': {
            backgroundColor: 'rgba(39, 174, 96, 0.08)',
          },
        }}
      >
        详细信息
      </Button>

      {/* 数据一致性警告 */}
      {hasDataInconsistency && (
        <Alert
          severity="warning"
          icon={<WarningIcon />}
          sx={{
            mb: 2,
            mt: 1,
            backgroundColor: 'rgba(255, 193, 7, 0.1)',
            borderColor: 'rgba(255, 193, 7, 0.3)',
          }}
        >
          <AlertTitle>数据一致性提醒</AlertTitle>
          检测到{dataInconsistencies.length}
          家公司数据差异。新增金额减处置金额应等于债权余额，请核实数据准确性。
        </Alert>
      )}

      {/* 图表容器 - 支持横向滚动 */}
      <Box
        sx={{
          overflowX: labels.length > 8 ? 'auto' : 'hidden',
          overflowY: 'hidden',
          width: '100%',
          pt: hasDataInconsistency ? 1 : 2,
        }}
      >
        <Box
          sx={{
            minWidth: labels.length > 8 ? `${labels.length * 80}px` : '100%',
            width: '100%',
            height: '350px',
          }}
        >
          <Bar data={chartData} options={options} style={{ height: '100%', width: '100%' }} />
        </Box>
      </Box>

      {/* 数据汇总信息 */}
      <Box
        sx={{
          mt: 2,
          pt: 2,
          borderTop: '1px solid #e0e0e0',
          display: 'flex',
          justifyContent: 'space-around',
          flexWrap: 'wrap',
          gap: 1,
          width: '100%',
          overflow: 'hidden', // 防止溢出
        }}
      >
        <Box sx={{ textAlign: 'center', flex: '1 1 0', minWidth: '120px', maxWidth: '25%' }}>
          <Box sx={{ fontSize: '12px', color: 'text.secondary', whiteSpace: 'nowrap' }}>
            新增债权总额
          </Box>
          <Box
            sx={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: 'rgba(39, 174, 96, 1)',
              whiteSpace: 'nowrap',
            }}
          >
            {totalNewDebtAmount.toFixed(2)} 万元
          </Box>
        </Box>
        <Box sx={{ textAlign: 'center', flex: '1 1 0', minWidth: '120px', maxWidth: '25%' }}>
          <Box sx={{ fontSize: '12px', color: 'text.secondary', whiteSpace: 'nowrap' }}>
            已处置总额
          </Box>
          <Box
            sx={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: 'rgba(46, 134, 193, 1)',
              whiteSpace: 'nowrap',
            }}
          >
            {totalDisposalAmount.toFixed(2)} 万元
          </Box>
        </Box>
        <Box sx={{ textAlign: 'center', flex: '1 1 0', minWidth: '120px', maxWidth: '25%' }}>
          <Box sx={{ fontSize: '12px', color: 'text.secondary', whiteSpace: 'nowrap' }}>
            债权余额总额
          </Box>
          <Box
            sx={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: 'rgba(231, 76, 60, 1)',
              whiteSpace: 'nowrap',
            }}
          >
            {totalRemainingBalance.toFixed(2)} 万元
          </Box>
        </Box>
        <Box sx={{ textAlign: 'center', flex: '1 1 0', minWidth: '100px', maxWidth: '25%' }}>
          <Box sx={{ fontSize: '12px', color: 'text.secondary', whiteSpace: 'nowrap' }}>
            处置比例
          </Box>
          <Box
            sx={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: 'rgba(46, 134, 193, 1)',
              whiteSpace: 'nowrap',
            }}
          >
            {totalProcessRate}%
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

NewDebtBalanceChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      companyName: PropTypes.string.isRequired,
      newDebtAmount: PropTypes.number,
      disposalAmount: PropTypes.number,
      remainingBalance: PropTypes.number,
    }),
  ),
  title: PropTypes.string,
};

NewDebtBalanceChart.defaultProps = {
  data: [],
  title: '各单位新增债权余额',
};

export default NewDebtBalanceChart;
