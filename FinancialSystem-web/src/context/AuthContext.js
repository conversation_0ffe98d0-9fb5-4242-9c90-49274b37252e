import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
  useRef,
  useCallback,
} from 'react';
import { jwtDecode } from 'jwt-decode';

export const AuthContext = createContext(null);

// 全局变量，用于在组件外部跟踪认证状态
let globalAuthInitialized = false;

// eslint-disable-next-line react/prop-types
export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const checkCountRef = useRef(0); // 用于限制重复检查次数
  const initialCheckDoneRef = useRef(false); // 跟踪初始检查是否完成

  const logout = useCallback(() => {
    localStorage.removeItem('auth');
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    setUser(null);
    setIsAuthenticated(false);
    // 更新全局状态
    globalAuthInitialized = true;
  }, []);

  const checkAuthStatus = useCallback(() => {
    // 防止无限检查循环
    if (checkCountRef.current > 3) {
      setLoading(false);
      return;
    }

    checkCountRef.current += 1;

    try {
      const authData = localStorage.getItem('auth');
      const storedToken = localStorage.getItem('token');

      // 简单记录状态，不输出具体内容避免过多日志
      console.log('本地存储状态:', {
        authData: authData ? '存在' : '缺失',
        token: storedToken ? '存在' : '缺失',
      });

      if (authData) {
        try {
          const userData = JSON.parse(authData);
          if (userData?.token) {
            try {
              const decoded = jwtDecode(userData.token);
              const currentTime = Date.now();

              if (decoded.exp * 1000 > currentTime) {
                // Token 有效
                setUser(userData);
                setIsAuthenticated(true);
                // 更新全局状态
                globalAuthInitialized = true;
              } else {
                logout();
              }
            } catch (tokenError) {
              console.error('解析 token 失败:', tokenError.message);
              logout();
            }
          } else if (storedToken) {
            try {
              const decoded = jwtDecode(storedToken);
              if (decoded.exp * 1000 > Date.now()) {
                // 基本用户信息
                const basicUser = {
                  token: storedToken,
                  username: decoded.sub,
                  role: Array.isArray(decoded.roles) ? decoded.roles[0] : decoded.roles || 'USER',
                  name: decoded.name || decoded.sub || '用户',
                  company: decoded.company || decoded.companyname || '',
                  department: decoded.department || '资产财务部',
                  loginTime: Date.now(),
                };

                setUser(basicUser);
                setIsAuthenticated(true);
                // 更新全局状态
                globalAuthInitialized = true;

                // 将 token 添加回 auth 对象
                localStorage.setItem('auth', JSON.stringify(basicUser));
              } else {
                logout();
              }
            } catch (error) {
              console.error('独立 token 解析失败:', error.message);
              logout();
            }
          } else {
            logout();
          }
        } catch (parseError) {
          console.error('解析 auth 数据失败:', parseError.message);
          if (storedToken) {
            try {
              const decoded = jwtDecode(storedToken);
              if (decoded.exp * 1000 > Date.now()) {
                // 使用 token 创建基本用户
                const basicUser = {
                  token: storedToken,
                  username: decoded.sub,
                  role: Array.isArray(decoded.roles) ? decoded.roles[0] : decoded.roles || 'USER',
                  name: decoded.name || decoded.sub || '用户',
                  company: decoded.company || decoded.companyname || '',
                  department: decoded.department || '资产财务部',
                  loginTime: Date.now(),
                };

                setUser(basicUser);
                setIsAuthenticated(true);
                // 更新全局状态
                globalAuthInitialized = true;

                // 同步到 localStorage
                localStorage.setItem('auth', JSON.stringify(basicUser));
              } else {
                logout();
              }
            } catch (error) {
              logout();
            }
          } else {
            logout();
          }
        }
      } else if (storedToken) {
        try {
          const decoded = jwtDecode(storedToken);
          if (decoded.exp * 1000 > Date.now()) {
            // 创建基本用户对象
            const basicUser = {
              token: storedToken,
              username: decoded.sub,
              role: Array.isArray(decoded.roles) ? decoded.roles[0] : decoded.roles || 'USER',
              name: decoded.name || decoded.sub || '用户',
              company: decoded.company || decoded.companyname || '',
              department: decoded.department || '资产财务部',
              loginTime: Date.now(),
            };

            localStorage.setItem('auth', JSON.stringify(basicUser));
            setUser(basicUser);
            setIsAuthenticated(true);
            // 更新全局状态
            globalAuthInitialized = true;
          } else {
            logout();
          }
        } catch (error) {
          logout();
        }
      } else {
        logout();
      }
    } catch (error) {
      console.error('检查认证状态时出错:', error);
      logout();
    } finally {
      setLoading(false);
      // 重置检查计数器 - 只在初始加载时限制，不影响用户手动重新检查
      setTimeout(() => {
        checkCountRef.current = 0;
      }, 5000);
    }
  }, [logout]);

  // 初始化时检查认证状态
  useEffect(() => {
    // 只在首次渲染时执行一次初始检查
    if (!initialCheckDoneRef.current) {
      checkAuthStatus();
      initialCheckDoneRef.current = true;
    }
  }, [checkAuthStatus]);

  // 监听 localStorage 变化
  useEffect(() => {
    // 定义存储事件处理程序
    const handleStorageChange = event => {
      if (event.key === 'auth' || event.key === 'token') {
        // 重置计数器以确保可以重新检查
        checkCountRef.current = 0;
        checkAuthStatus();
      }
    };

    // 添加事件监听器
    window.addEventListener('storage', handleStorageChange);

    // 清理函数
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [checkAuthStatus]);

  const login = userData => {
    try {
      checkCountRef.current = 0; // 重置检查计数

      // 确保用户数据包含所有必要字段
      const enhancedUserData = {
        ...userData,
        username: userData.username || '用户',
        role: userData.role || 'USER',
        name: userData.name || userData.username || '用户',
        company: userData.company || userData.companyname || '',
        department: userData.department || '资产财务部',
        loginTime: userData.loginTime || Date.now(),
      };

      // 存储用户数据
      localStorage.setItem('auth', JSON.stringify(enhancedUserData));
      localStorage.setItem('token', enhancedUserData.token);

      setUser(enhancedUserData);
      setIsAuthenticated(true);
      // 更新全局状态
      globalAuthInitialized = true;
    } catch (error) {
      console.error('登录处理时出错:', error);
      // 确保即使出错，我们也保存基本数据
      localStorage.setItem('auth', JSON.stringify(userData));
      localStorage.setItem('token', userData.token);
      setUser(userData);
      setIsAuthenticated(true);
      // 更新全局状态
      globalAuthInitialized = true;
    }
  };

  const contextValue = useMemo(
    () => ({
      isAuthenticated,
      loading,
      user,
      token: user?.token || localStorage.getItem('token'),
      login,
      logout,
      checkAuthStatus,
      initialized: globalAuthInitialized,
    }),
    [isAuthenticated, loading, user, login, logout, checkAuthStatus],
  );

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
